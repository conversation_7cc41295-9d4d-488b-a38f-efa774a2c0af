<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Space\MongoDocuments\Document\Settings;

/**
 * Settings Service to handle all settings-related operations.
 */
class SettingsService
{
    public function __construct(
        private MongoDBService $mongoService,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Get O2X settings by brand and source
     */
    public function getO2xSettings(string $brand, string $source = "APP"): array
    {
        try {
            $filter = [
                'brand' => $brand,
                'source' => $source,
                'culture' => '',
                '$or' => [
                    ["settingsData.o2x.code" => "o2x"],
                    ["settingsData.config.code" => "o2x"]
                ]
            ];

            // Using MongoDB service to find settings
            $settings = $this->mongoService->findSettingsByFilter($filter);
            $settingsDatas = $settings ? $settings->getSettingsData() : [];
            
            if ('APP' == $source) {
                $data = current(array_filter($settingsDatas, function($item) {
                    $code = $item['config']['code'] ?? '';
                    return $code == 'o2x';
                }));
                
                if ($data) {
                    $config = $data['config'] ?? [];
                    $result = [...$data, ...$config];
                    unset($result['config']);
                } else {
                    $result = $settingsDatas['o2x'] ?? [];
                }

                return $result ?? [];
            }
            
            return $settingsDatas['o2x'] ?? [];
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error while getting o2x settings ', __METHOD__),
                [
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return [];
        }
    }

    /**
     * Find settings by type, brand, and country
     */
    public function findSettingsByTypeBrandAndCountry(string $type, string $brand, string $country): ?Settings
    {
        return $this->mongoService->findSettingsByTypeBrandAndCountry($type, $brand, $country);
    }

    /**
     * Find settings by filter
     */
    public function findSettingsByFilter(array $filter): ?Settings
    {
        return $this->mongoService->findSettingsByFilter($filter);
    }
}
