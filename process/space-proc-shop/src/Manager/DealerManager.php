<?php

namespace App\Manager;

use App\DataMapper\DealerDataMapper;
use App\DtoRequest\XfEmeaParameterRequest;
use App\DtoResponse\DealerResponseDto;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\DealerService;
use App\Service\EnhancedDealerService;
use App\Service\SettingsService;
use App\Trait\LoggerTrait;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\XpResponseTransformer;
use Symfony\Component\Serializer\SerializerInterface;
use Doctrine\ODM\MongoDB\DocumentManager;

/**
 * Dealer Manager.
 */
class DealerManager
{
    use LoggerTrait;

    private const COLLECTION = 'settings';

    public function __construct(
        private DealerService $service,
        private EnhancedDealerService $enhancedDealerService,
        private SerializerInterface $serializer,
        private DealerDataMapper $dealerDataMapper,
        private SettingsService $settingsService,
        private DocumentManager $documentManager
    ) {
    }

    /**
     * get Dealer data with enhanced MongoDB integration.
     */
    public function getDealerList($params): ResponseArrayFormat{
        try {
            $this->logger->info('=> ' . __METHOD__ . ' => Enhanced dealer list request', $params);

            $dealer = null;
            $params['o2x'] = $this->getO2xSettings($params['brand'], $params['source']);

            // Try enhanced MongoDB service first
            $response = $this->callEnhancedDealerList($params);

            // Fallback to original service if enhanced service fails
            if ($response['code'] != 200) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Enhanced service failed, falling back to original service');
                $response = $this->callDealerList($params);
            }

            if ($response['code'] != 200) {
                return new ErrorResponse(
                    $response ['data'] ?? 'Dealers not found',
                    $response['code']
                );
            }

            $data = $response['data']['success'] ?? [];
            if ($data) {
                $dealer = $this->getMappedDealerData($data, $params);
            }

            // Handle O2X model requests
            if(isset($params['model']) && !empty($params['model'])){
                if (strtoupper($params['model']) == 'O2X') {
                    $params['brand'] = 'XX';

                    // Try enhanced service for XX brand
                    $xxBrandResponse = $this->callEnhancedDealerList($params);

                    // Fallback to original service if needed
                    if ($xxBrandResponse['code'] != 200) {
                        $xxBrandResponse = $this->callDealerList($params);
                    }

                    if ($xxBrandResponse['code'] != 200) {
                        return new ErrorResponse(
                            $xxBrandResponse ['data'] ?? 'Dealers not found',
                            $xxBrandResponse['code']
                        );
                    }
                    $data = $xxBrandResponse['data']['success'] ?? [];
                    $dealer = $this->getMappedDealerData($data, $params, true, $dealer);
                }else{
                    return new SuccessResponse([]);
                }
            }

            return new SuccessResponse($dealer->getSuccess());

        } catch (\Exception $e) {
            $this->logger->error('Error => ' . __METHOD__ . ' Catched Exception ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e->getMessage()
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * get Xf Emea Dealer data.
     */
    public function getDealerXfEmeaList(XfEmeaParameterRequest $params): ResponseArrayFormat
    {
        $this->logger->info('=> Call Dealer for xf Emea API : '.__METHOD__.' with parameters : ', (array)$params);
        try {
            $response = $this->service->getXfEmeaDealerList($params);
            if(isset($response->getData()['error'])) {
                return new ErrorResponse(
                    $response->getData()['error']['message'] ?? '',
                    $response->getCode()
                );
            }
            $xfEmeaResponse = XfEmeaResponseTransformer::mapper(
                $response->getData()['success'] ?? [],
                $params
            );

            return new SuccessResponse($xfEmeaResponse->getSuccess());
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.' Catched Exception DealerManager::getDealerList for xf Emea '.$e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getO2xSettings(string $brand, $source = "APP"): array
    {
        try {
            // Delegate to the SettingsService
            return $this->settingsService->getO2xSettings($brand, $source);
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error while getting o2x settings ', __METHOD__),
                [
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return [];
        }
    }

    private function getMappedDealerData(array $data, array $params, bool $withXXCall = false, ?DealerResponseDto $dealerData = null): DealerResponseDto{
        return XpResponseTransformer::mapper(
            $data ?? [],
            $params,
            $withXXCall,
            $dealerData
        );

    }

    private function callDealerList(array $params): ?array{
        $this->logger->info('=> Call Dealer API with : '.__METHOD__.' with parameters : ', $params);
        $response = $this->service->getDealerList($params);
        return [
            'data' => $response->getData() ?? null,
            'code' => $response->getCode()
        ];
    }

    /**
     * Call enhanced dealer list service with MongoDB integration
     */
    private function callEnhancedDealerList(array $params): ?array{
        try {
            $this->logger->info('=> Call Enhanced Dealer Service with : '.__METHOD__.' with parameters : ', $params);
            $response = $this->enhancedDealerService->getDealerList($params);
            return [
                'data' => $response->getData() ?? null,
                'code' => $response->getCode()
            ];
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Enhanced dealer service error: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e->getMessage()
            ]);

            // Return error response to trigger fallback
            return [
                'data' => ['error' => ['message' => $e->getMessage()]],
                'code' => 500
            ];
        }
    }
}
