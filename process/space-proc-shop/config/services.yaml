# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    # MongoDB Atlas API (legacy)
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    mongo_db.url: "%env(MONGO_ATLAS_BASE_URL)%"

    # MongoDB ODM
    mongodb.url: "%env(MONGO_DB_URL)%"
    mongodb.db: "%env(MONGODB_DB)%"

    tomtom_base_url: '%env(TOMTOM_URL)%'
    tomtom_api_key: '%env(TOMTOM_API_KEY)%'
    env(MONGODB_URL): ''
    env(MONGODB_DB): ''

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/Kernel.php'

    # MongoDB Atlas API services (legacy - disabled, using MongoDB ODM instead)
    # App\Service\MongoAtlasQueryService:
    #     arguments:
    #         $database: '%mongo_db.database%'
    #         $dataSource: '%mongo_db.datasource%'

    # App\Connector\MongoAtlasApiConnector:
    #     arguments:
    #         $mongoApp: '%mongo_db.app%'

    # MongoDB ODM services
    Space\MongoDocuments\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $logger: '@logger'

    App\Connector\SysSamsDataConnector:
        arguments:
            $url: "%env(MS_SYS_SAMS_DATA_URL)%"

    App\Connector\F2mConnector:
        arguments:
            $url: "%env(F2M_URL)%"

    App\Connector\SysAPDVConnector:
        arguments:
            $url: "%env(MS_SYS_APDV_URL)%"

    App\Connector\SysServiceAdvisorConnector:
        arguments:
            $url: "%env(MS_SYS_SERVICE_ADVISOR_URL)%"

    App\Service\TomTomService:
        arguments:
            $httpClient: '@http_client'
            $baseUrl: '%tomtom_base_url%'
            $apiKey: '%tomtom_api_key%'

    App\Service\CorvetSysConnector:
        arguments:
            $url: "%env(MS_SYS_CORVET_DATA_URL)%"

    App\Service\EvRoutingService:
        arguments:
            $client: '@App\Connector\CustomHttpClient'
            $boApiBaseUrl: '%env(SPACE_BO_API_BASE_URL)%'

    # Enhanced Dealer Service with MongoDB ODM
    App\Service\EnhancedDealerService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $fallbackService: '@App\Service\DealerService'

    # Settings Service with MongoDB integration
    App\Service\SettingsService:
        arguments:
            $mongoService: '@Space\MongoDocuments\Service\MongoDBService'
            $logger: '@logger'

    # Dealer Manager with explicit argument mapping to fix dependency injection
    App\Manager\DealerManager:
        arguments:
            $service: '@App\Service\DealerService'
            $enhancedDealerService: '@App\Service\EnhancedDealerService'
            $serializer: '@serializer'
            $dealerDataMapper: '@App\DataMapper\DealerDataMapper'
            $settingsService: '@App\Service\SettingsService'
            $documentManager: '@doctrine_mongodb.odm.document_manager'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
